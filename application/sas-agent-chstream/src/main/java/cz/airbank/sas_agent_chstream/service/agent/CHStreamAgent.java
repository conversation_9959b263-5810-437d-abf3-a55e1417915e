package cz.airbank.sas_agent_chstream.service.agent;

import cz.airbank.sas.campaign.email.SentCampaignEmailEvent;
import cz.airbank.sas_agent_chstream.cache.CampaignCache;
import cz.airbank.sas_agent_chstream.cache.CampaignCacheEntry;
import cz.airbank.sas_agent_chstream.cache.CreativeCache;
import cz.airbank.sas_agent_chstream.cache.CreativeCacheEntry;
import cz.airbank.sas_agent_chstream.cache.TaskCache;
import cz.airbank.sas_agent_chstream.cache.TaskCacheEntry;
import cz.airbank.sas_agent_chstream.common.Util;
import cz.airbank.sas_agent_chstream.config.ApplicationConfig;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import cz.airbank.sas_agent_chstream.kafka.producer.SentCampaignEmailEventKafkaProducer;
import cz.airbank.sas_agent_chstream.service.Agent;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Original SAS code
 */
@Service
@Slf4j
public class CHStreamAgent implements Agent {

    private final ApplicationConfig applicationConfig;
    private final SentCampaignEmailEventKafkaProducer producer;
    private final DataSource dataSource;
    private final TaskCache taskCache;
    private final CreativeCache creativeCache;
    private final CampaignCache campaignCache;

    /* Mandatory field for saving message to db*/
    private static final String MANDATORY_FIELDS = "CONTACT_ID|CONTACT_DT|CONTACT_DTTM|IDENTITY_ID|LEAD_ID|CI360_CONTACT_CHANNEL_NM|INSERTED_DTTM|UPDATED_DTTM|CAMP_TYPE_CODE|CAMP_SUBTYPE_CODE|ARCHIVED_FLAG";

    public CHStreamAgent(ApplicationConfig applicationConfig, SentCampaignEmailEventKafkaProducer producer, DataSource dataSource, TaskCache taskCache, CreativeCache creativeCache, CampaignCache campaignCache) {
        this.applicationConfig = applicationConfig;
        this.producer = producer;
        this.dataSource = dataSource;
        this.taskCache = taskCache;
        this.creativeCache = creativeCache;
        this.campaignCache = campaignCache;
    }

    @Override
    public AgentType getAgentType() {
        return AgentType.CH_STREAM;
    }

    @Override
    public void processMessage(String event) throws Exception {
        throw new UnsupportedOperationException("Use processMessage(JSONObject event)");
    }

    @Override
    public void processMessage(JSONObject attr) throws Exception {

        /*--- load  Custom Properties from caches ---*/
        String taskId = Util.jsonReadString(attr, "task_id");
        String taskVersionId = Util.jsonReadString(attr, "task_version_id");
        String creativeId = Util.jsonReadString(attr, "creative_id");
        String creativeVersionId = Util.jsonReadString(attr, "creative_version_id");

        //caches
        TaskCacheEntry taskCacheEntry = taskCache.getTask(taskVersionId, taskId);
        if (taskCacheEntry == null) {
            log.warn("Failed to retrieve task cache for taskVersionId {} and taskId {}. Stop processing event", taskVersionId, taskId);
            return;
        }
        CreativeCacheEntry creativeCacheEntry = creativeCache.getCreative(creativeVersionId, creativeId);
        CampaignCacheEntry campaignCacheEntry = campaignCache.getCamapign(taskCacheEntry.tsk_comm_camp_name.value);

        //parse all parameters
        HashMap<String, Object> params = evaluateParameters(attr, taskCacheEntry, creativeCacheEntry, campaignCacheEntry);

        //validation
        boolean valid = validateParameters(params, MANDATORY_FIELDS);

        if (valid) {
            //save event to db
            saveToDb(params);

            //process email
            processEmail(attr, params, taskCacheEntry, campaignCacheEntry);
        }
    }

    private void processEmail(JSONObject attr, HashMap<String, Object> params, TaskCacheEntry te, CampaignCacheEntry ce) {

        String eventName = Util.jsonReadString(attr, "eventName");
        String channelType = Util.jsonReadString(attr, "channelType");

        if (eventName.equalsIgnoreCase("c_send") && channelType.equalsIgnoreCase("email")) {

            SentCampaignEmailEvent event = new SentCampaignEmailEvent();
            event.setCuid(Long.parseLong(params.get("SUBJECT_ID").toString()));
            event.setCreator("SAS360");
            event.setExternalId(params.get("LEAD_ID").toString());
            event.setCommunicationKind(params.get("COMM_TYPE_CODE").toString());
            List<String> products = new ArrayList<>();
            if (te.tsk_camp_product.hasValue()) {
                String values = te.tsk_camp_product.value;
                List<String> valueList = Arrays.asList(values.split(","));
                products.addAll(valueList);
            }
            event.setProducts(products);

            event.setTaskVersionId(params.get("TASK_VERSION_ID").toString());
            event.setCampaignCode(params.get("CAMPAIGN_MESSAGE_CD").toString());
            event.setCampaignName(ce == null ? null : ce.camp_name.value);
            event.setCommunicationCode(params.get("COMM_TYPE_CODE").toString());
            event.setCommunicationName(te.taskName.value);
            if (te.tsk_camp_buss_cause_cd.hasValue()) {
                event.setBusinessSummaryCauseCode(te.tsk_camp_buss_cause_cd.value);
            }
            event.setEmailSubject(Util.jsonReadString(attr, "emailSubject"));
            event.setEmailURL(params.get("EMAIL_IMPRINT_URL").toString());

            LocalDateTime now = LocalDateTime.now();
            String formattedNow = getLocalDateTimeInIsoOffset(now);
            event.setCreated(formattedNow);
            event.setTimeSent(getLocalDateTimeInIsoOffset(params.get("TIMESTAMP").toString()));

            producer.publish(event.getCuid(), event);
        }
    }

    private HashMap<String, Object> evaluateParameters(JSONObject jsonEvent, TaskCacheEntry taskCache, CreativeCacheEntry creativeCache, CampaignCacheEntry campaignCache) {

        HashMap<String, Object> pars = new HashMap<>();

        pars.put("CONTACT_ID", Util.jsonReadString(jsonEvent, "guid"));

        //cloud unix timestamp -> contact_dttm and contact_dt
        String timestamp = Util.jsonReadString(jsonEvent, "timestamp");
        pars.put("TIMESTAMP", timestamp);
        Timestamp contact_dttm = new Timestamp(Long.parseLong(timestamp));
        pars.put("CONTACT_DTTM", contact_dttm);
        LocalDateTime localDateTime = contact_dttm.toLocalDateTime();
        LocalDate localDate = localDateTime.toLocalDate();
        Timestamp contact_dt = Timestamp.valueOf(localDate.atStartOfDay());
        pars.put("CONTACT_DT", contact_dt);

        pars.put("RTC_ID", Util.jsonReadString(jsonEvent, "response_tracking_code"));
        pars.put("BUSINESS_CONTEXT_CD", null);
        pars.put("IDENTITY_ID", Util.jsonReadString(jsonEvent, "datahub_id"));
        pars.put("SUBJECT_ID", Util.jsonReadString(jsonEvent, "subject_id"));
        pars.put("CUSTOMER_ID", Util.jsonReadString(jsonEvent, "customer_id"));
        pars.put("VISITOR_ID", Util.jsonReadString(jsonEvent, "vid"));
        pars.put("LOGIN_ID", Util.jsonReadString(jsonEvent, "login_id"));
        //lead_id
        String leadId = getLeadId(jsonEvent);
        pars.put("LEAD_ID", leadId);

        pars.put("TASK_ID", taskCache.taskId);
        pars.put("CREATIVE_ID", Util.jsonReadString(jsonEvent, "creative_id"));
        pars.put("MESSAGE_ID", Util.jsonReadString(jsonEvent, "message_id"));
        pars.put("CI360_CONTACT_CHANNEL_NM", Util.jsonReadString(jsonEvent, "channelType"));
        pars.put("CONTACT_CHANNEL_CD", getContactChannel(jsonEvent, taskCache));
        pars.put("CONTACT_STATUS_CD", "_11");
        pars.put("OCCURRENCE_ID", Util.jsonReadString(jsonEvent, "occurrence_id"));
        pars.put("TASK_VERSION_ID", taskCache.taskVersionId);
        pars.put("CREATIVE_VERSION_ID", Util.jsonReadString(jsonEvent, "creative_version_id"));
        pars.put("SPOT_ID", Util.jsonReadString(jsonEvent, "spot_id"));
        pars.put("IMPRINT_ID", Util.jsonReadString(jsonEvent, "imprint_id"));
        pars.put("EMAIL_IMPRINT_URL", Util.jsonReadString(jsonEvent, "emailImprintURL"));
        pars.put("SESSION_ID", Util.jsonReadString(jsonEvent, "session"));
        if (Util.jsonReadString(jsonEvent, "eventName").contains("CONTROL_GROUP")) {
            pars.put("CONTROL_GROUP_FLG", "Y");
        } else {
            pars.put("CONTROL_GROUP_FLG", "N");
        }
        pars.put("AUDIENCE_ID", Util.jsonReadString(jsonEvent, "audience_id"));
        pars.put("AUD_OCCURENCE_ID", Util.jsonReadString(jsonEvent, "aud_occurence_id"));
        Timestamp tsNow = new Timestamp(System.currentTimeMillis());
        pars.put("INSERTED_DTTM", tsNow);
        pars.put("UPDATED_DTTM", tsNow);
        pars.put("CAMPAIGN_MESSAGE_CD", taskCache.tsk_comm_camp_name.value);

        //values from cache
        pars.put("CAMP_TYPE_CODE", taskCache.tsk_camp_type.value);
        pars.put("CAMP_SUBTYPE_CODE", taskCache.tsk_camp_subtype.value);
        pars.put("COMM_TYPE_CODE", taskCache.tsk_camp_comm_type.value);
        pars.put("CONTACT_POLICY_TYPE_CODE", taskCache.tsk_cp_type.value);
        pars.put("CONTACT_POLICY_PRODUCT_CODE", (creativeCache != null && creativeCache.cre_camp_cp_product.hasValue()) ? creativeCache.cre_camp_cp_product.value : taskCache.tsk_cp_product.value);

        return pars;
    }

    private String getContactChannel(JSONObject jsonEvent, TaskCacheEntry te) {

        String channelType = Util.jsonReadString(jsonEvent, "channelType");
        String eventName = Util.jsonReadString(jsonEvent, "eventName");
        String task_type = Util.jsonReadString(jsonEvent, "task_type");

        if (eventName.equalsIgnoreCase("c_send") && channelType.equalsIgnoreCase("email")) {
            return "EMAIL";
        }
        if (eventName.equalsIgnoreCase("c_inAppSend") && channelType.equalsIgnoreCase("mobile")) {
            return "INAPP";
        }
        if (eventName.equalsIgnoreCase("c_impression_viewable") && channelType.equalsIgnoreCase("mobile")) {
            return "MA_BANNER";
        }
        if (eventName.equalsIgnoreCase("c_advertisingAudienceUpload") && task_type.equalsIgnoreCase("Google Ads")) {
            return "GGL";
        }
        if (eventName.equalsIgnoreCase("c_advertisingAudienceUpload") && task_type.equalsIgnoreCase("Facebook Audience")) {
            return "FCB";
        }

        if (te.tsk_comm_chan_code.value == null)
            return "XNA";
        else
            return te.tsk_comm_chan_code.value;

    }

    private void saveToDb(HashMap<String, Object> pars) throws Exception {

        //save message to DB table queue
        String sql = " INSERT INTO " + applicationConfig.getDatabaseName() + ".CIE_CONTACT_HISTORY_STREAM (CONTACT_ID,CONTACT_DT,CONTACT_DTTM," +
                " RTC_ID, BUSINESS_CONTEXT_CD, IDENTITY_ID, SUBJECT_ID, CUSTOMER_ID," +
                "VISITOR_ID, LOGIN_ID, LEAD_ID, TASK_ID, CREATIVE_ID, MESSAGE_ID," +
                "CI360_CONTACT_CHANNEL_NM, CONTACT_CHANNEL_CD, CONTACT_STATUS_CD, OCCURRENCE_ID, TASK_VERSION_ID, " +
                "CREATIVE_VERSION_ID, SPOT_ID, IMPRINT_ID, EMAIL_IMPRINT_URL, SESSION_ID, CONTROL_GROUP_FLG, AUDIENCE_ID," +
                "AUD_OCCURENCE_ID, INSERTED_DTTM, UPDATED_DTTM, CAMPAIGN_MESSAGE_CD, CAMP_TYPE_CODE, CAMP_SUBTYPE_CODE, CAMP_PRODUCT_CODE, COMM_TYPE_CODE, CONTACT_POLICY_TYPE_CODE, CONTACT_POLICY_PRODUCT_CODE ) " +
                " VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";

        try (
                Connection con = dataSource.getConnection();
                PreparedStatement st = con.prepareStatement(sql);
        ) {
            setStringParameter(st, 1, pars.get("CONTACT_ID"));
            setTimestampParameter(st, 2, pars.get("CONTACT_DT"));
            setTimestampParameter(st, 3, pars.get("CONTACT_DTTM"));
            setStringParameter(st, 4, pars.get("RTC_ID"));
            setStringParameter(st, 5, pars.get("BUSINESS_CONTEXT_CD"));
            setStringParameter(st, 6, pars.get("IDENTITY_ID"));
            setStringParameter(st, 7, pars.get("SUBJECT_ID"));
            setStringParameter(st, 8, pars.get("CUSTOMER_ID"));
            setStringParameter(st, 9, pars.get("VISITOR_ID"));
            setStringParameter(st, 10, pars.get("LOGIN_ID"));
            setStringParameter(st, 11, pars.get("LEAD_ID"));
            setStringParameter(st, 12, pars.get("TASK_ID"));
            setStringParameter(st, 13, pars.get("CREATIVE_ID"));
            setStringParameter(st, 14, pars.get("MESSAGE_ID"));
            setStringParameter(st, 15, pars.get("CI360_CONTACT_CHANNEL_NM"));
            setStringParameter(st, 16, pars.get("CONTACT_CHANNEL_CD"));
            setStringParameter(st, 17, pars.get("CONTACT_STATUS_CD"));
            setStringParameter(st, 18, pars.get("OCCURRENCE_ID"));
            setStringParameter(st, 19, pars.get("TASK_VERSION_ID"));
            setStringParameter(st, 20, pars.get("CREATIVE_VERSION_ID"));
            setStringParameter(st, 21, pars.get("SPOT_ID"));
            setStringParameter(st, 22, pars.get("IMPRINT_ID"));
            setStringParameter(st, 23, pars.get("EMAIL_IMPRINT_URL"));
            setStringParameter(st, 24, pars.get("SESSION_ID"));
            setStringParameter(st, 25, pars.get("CONTROL_GROUP_FLG"));
            setStringParameter(st, 26, pars.get("AUDIENCE_ID"));
            setStringParameter(st, 27, pars.get("AUD_OCCURENCE_ID"));
            setTimestampParameter(st, 28, pars.get("INSERTED_DTTM"));
            setTimestampParameter(st, 29, pars.get("UPDATED_DTTM"));
            setStringParameter(st, 30, pars.get("CAMPAIGN_MESSAGE_CD"));
            setStringParameter(st, 31, pars.get("CAMP_TYPE_CODE"));
            setStringParameter(st, 32, pars.get("CAMP_SUBTYPE_CODE"));
            setStringParameter(st, 33, pars.get("CAMP_PRODUCT_CODE"));
            setStringParameter(st, 34, pars.get("COMM_TYPE_CODE"));
            setStringParameter(st, 35, pars.get("CONTACT_POLICY_TYPE_CODE"));
            setStringParameter(st, 36, pars.get("CONTACT_POLICY_PRODUCT_CODE"));

            st.execute();
            log.debug("Event inserted into database");
        } catch (SQLException e) {
            log.error("ErrorCode:CH_02 - DB - not possible to write payload to DB", e);
        }
    }

    protected String getLeadId(JSONObject jsonEvent) {

        String leadId;

        //test if direct event
        boolean directEvent = false;
        String event = Util.jsonReadString(jsonEvent, "event");
        if (event.equalsIgnoreCase(applicationConfig.getEvents().get(getAgentType()).getDirectEvent())) {
            directEvent = true;
        }

        String rtc = Util.jsonReadString(jsonEvent, "response_tracking_code");
        String datahub = Util.jsonReadString(jsonEvent, "datahub_id");
        String timestamp = Util.jsonReadString(jsonEvent, "timestamp");

        if (directEvent) {
            leadId = datahub + "_" + rtc;
        } else {
            leadId = datahub + "_" + rtc + "_" + timestamp;
        }
        return leadId;
    }

    protected boolean validateParameters(HashMap<String, Object> pars, String mandatoryFields) {
        String logMsg = "Parameters validation: ";

        //check if all parameters exists
        boolean res = true;

        for (Map.Entry<String, Object> me : pars.entrySet()) {
            String key = me.getKey();
            Object valObject = me.getValue();

            if (valObject == null || valObject.toString().isEmpty()) {
                //parameter without value
                if (mandatoryFields.contains(key)) {
                    //mandatory
                    res = false;
                    logMsg += String.format("%s = %s  missing value for mandatory field !!!!!!!, ", key, "");
                } else {
                    //optional parameter
                    logMsg += String.format("%s = %s, ", key, "");
                }
            } else {
                //parameter has value
                String valString;
                if (valObject instanceof Timestamp ts) {
                    valString = ts.toString();
                } else {
                    valString = valObject.toString();
                }
                logMsg += String.format("%s = %s, ", key, valString);
            }
        }

        if (res) {
            log.debug(logMsg);
            log.debug("Validation: OK");
        } else {
            log.error(logMsg);
            log.error("ErrorCode:CH_02 - Validation error - missing mandatory attributes in payload");
        }
        return res;
    }

    private void setStringParameter(PreparedStatement st, int index, Object value) throws SQLException {
        if (value == null || value.toString().isEmpty()) {
            st.setNull(index, Types.VARCHAR);
        } else {
            st.setString(index, value.toString());
        }
    }

    private void setTimestampParameter(PreparedStatement st, int index, Object value) throws SQLException {
        if (value == null) {
            st.setNull(index, Types.TIMESTAMP);
        } else {
            st.setTimestamp(index, (Timestamp) value);
        }
    }

    private static String getLocalDateTimeInIsoOffset(LocalDateTime dt) {
        ZonedDateTime ldtZoned = dt.atZone(ZoneId.systemDefault());
        return DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(ldtZoned);
    }

    private static String getLocalDateTimeInIsoOffset(String timestamp) {
        try {
            long timestampLong = Long.parseLong(timestamp);
            Instant instant = Instant.ofEpochMilli(timestampLong);
            ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(instant, ZoneId.systemDefault());
            return zonedDateTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
        } catch (Exception e) {
            return null;
        }
    }
}

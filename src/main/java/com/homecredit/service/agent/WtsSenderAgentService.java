package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.enumeration.FailedStatus;
import com.homecredit.enumeration.MessageStatus;
import com.homecredit.model.Attribute;
import com.homecredit.model.MessageWrapper;
import com.homecredit.model.WtsMessage;
import com.homecredit.service.AbstractSenderAgentService;
import com.homecredit.service.ExtApiGwService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.homecredit.enumeration.MessageStatus.ERROR;
import static com.homecredit.enumeration.MessageStatus.PROCESSED;

@Slf4j
@Service
@ConditionalOnProperty(name = "hci.agents.WTS.enabled", havingValue = "true")
public class WtsSenderAgentService extends AbstractSenderAgentService {

    private final RabbitTemplate rabbitTemplate;
    private final ExtApiGwService extApiGwService;

    public WtsSenderAgentService(
            ApplicationConfig applicationConfig,
            DataSource dataSource,
            ObjectMapper objectMapper,
            RabbitTemplate rabbitTemplate,
            ExtApiGwService extApiGwService) {
        super(applicationConfig, dataSource, objectMapper);
        this.rabbitTemplate = rabbitTemplate;
        this.extApiGwService = extApiGwService;
    }

    @Override
    public Agent getAgent() {
        return Agent.WTS;
    }

    @Scheduled(cron = "${hci.cron.process-data}")
    public void onSchedule() {
        processData();
    }

    public void processData() {
        List<Map<String, Object>> newRows = getNewRows(getAgent());
        for (Map<String, Object> row : newRows) {

            Long id = (Long) getColumnValue(row, "ID");
            log.debug("Processing message with ID {} ...", id);
            WtsMessage message = prepareMessage(row);
            if (message.getTplId() == null) {
                log.debug("Message with ID {} has null TPL ID. Obtaining TPL ID from REST API call.", id);
                Long tplId = extApiGwService.getTplId(id, message.getCreativeId(), getAgent());
                message.setTplId(tplId);
            } else if (isMessageExpired(message)) {
                handleError(FailedStatus.EXPIRED, MessageStatus.FAILED, message, "Message is expired");
            } else if (isMessageInvalid(message)) {
                handleError(FailedStatus.INVALID, MessageStatus.FAILED, message, "Message is invalid");
            } else if (message.getContactId() == null) {
                log.debug("Message with ID {} has null contactId", id);
                if (isTooLateForProcessingContactId(message.getGeneratedDateTime())) {
                    log.debug("Message with ID {} is more than {} minutes old, marking as expired", message.getId(), applicationConfig.getContactIdTimeout());
                    handleError(FailedStatus.EXPIRED, MessageStatus.FAILED, message, "Message has null externalID for more than 30 minutes");
                } else {
                    log.debug("Message with ID {} has null contactId, but is less than {} minutes old, will try processing later", message.getId(), applicationConfig.getContactIdTimeout());
                }
            } else {
                message = populateMessageTemplate(message, row);
                if (message == null) {
                    continue;
                }
                message = populateButtonParameters(message, row);
                if (message == null) {
                    continue;
                }
                sendMessageToRabbit(message, id);
            }
        }
    }

    private void sendMessageToRabbit(WtsMessage message, Long id) {
        log.debug("Preparing to send message with ID {} to RabbitMQ", message.getId());
        MessageWrapper messageWrapper = new MessageWrapper();
        messageWrapper.setMessage(Collections.singletonList(message));

        String jsonMessage;
        try {
            jsonMessage = objectMapper.writeValueAsString(messageWrapper);
        } catch (JsonProcessingException e) {
            handleError(FailedStatus.INVALID, ERROR, message, "Failed to write data to JSON: " + e.getMessage());
            return;
        }
        log.debug("JSON message: {}", jsonMessage);

        try {
            rabbitTemplate.convertAndSend(
                    applicationConfig.getRabbit().get(getAgent()).getExchange(),
                    applicationConfig.getRabbit().get(getAgent()).getRoutingKey(),
                    jsonMessage,
                    m -> {
                        m.getMessageProperties().getHeaders().put("SYSTEM_CODE", message.getSystemCode());
                        m.getMessageProperties().getHeaders().put("REQUEST_ID", message.getExternalId());
                        m.getMessageProperties().getHeaders().put("priority", 0);
                        m.getMessageProperties().getHeaders().put("CorrelationID", message.getExternalId());
                        m.getMessageProperties().getHeaders().put("Type", "JMSType");
//                m.getMessageProperties().setPriority(0);
//                m.getMessageProperties().setCorrelationId(message.getExternalId());
//                m.getMessageProperties().setType("JMSType");
                        m.getMessageProperties().setContentType("application/json");
                        return m;
                    });
        } catch (Exception e) {
            handleError(FailedStatus.INVALID, MessageStatus.FAILED, message, "Failed to sent to RabbitMQ error " + e.getMessage());
            return;
        }
        updateDataTable(PROCESSED, message, null);
    }


    private WtsMessage prepareMessage(Map<String, Object> row) {
        Long tplId = null;
        if (getColumnValue(row, "TPL_ID") != null) {
            tplId = (Long) getColumnValue(row, "TPL_ID");
        }
        String cuid = (String) getColumnValue(row, "cuid");

        WtsMessage message = new WtsMessage();
        message.setExternalId("WTS_" + getColumnValue(row, "contact_id"));
        message.setContactId((String) getColumnValue(row, "contact_id"));
        message.setRecipient((String) getColumnValue(row, "PHONE_NUMBER"));
        message.setSystemCode(applicationConfig.getPayload().get(getAgent()).getSystemCode());
        message.setMessageCode((String) getColumnValue(row, "MESSAGE_CODE"));
        message.setCuid(cuid);
        message.setHeaderText((String) getColumnValue(row, "HEADER_TEXT"));
        message.setBodyText((String) getColumnValue(row, "BODY_TEXT"));
        message.setFooterText((String) getColumnValue(row, "FOOTER_TEXT"));
        message.setExpires(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "ExpiresOnDttm"))));
        message.setGeneratedDateTime((ZonedDateTime) getColumnValue(row, "generatedatedttm"));
        message.setPriority((String) getColumnValue(row, "PRIORITY_LIST"));
        message.setReportLevel(applicationConfig.getPayload().get(getAgent()).getReportLevel());
        List<Attribute> attributes = new ArrayList<>();
        if (cuid != null) {
            attributes.add(new Attribute("CUID", cuid));
        }
        attributes.add(new Attribute("CHANNEL", (String)getColumnValue(row, "CHANNEL")));
        attributes.add(new Attribute("FEATURE", (String)getColumnValue(row, "FEATURE")));
        attributes.add(new Attribute("JATIS_ACCOUNT_ID", (String)getColumnValue(row, "JATIS_ACCOUNT_ID")));
        attributes.add(new Attribute("WA_TEMPLATE_ID", (String)getColumnValue(row, "JATIS_TEMPLATE_ID")));
        attributes.add(new Attribute("WA_LANGUAGE", (String)getColumnValue(row, "TPL_LANGUAGE")));
        if (getColumnValue(row, "jts_media_content_type") != null) {
            attributes.add(new Attribute("WA_MEDIA_CONTENT_TYPE", (String) getColumnValue(row, "jts_media_content_type")));
        } else if (getColumnValue(row, "MEDIA_CONTENT_TYPE") != null) {
            attributes.add(new Attribute("WA_MEDIA_CONTENT_TYPE", (String) getColumnValue(row, "MEDIA_CONTENT_TYPE")));
        }
        if (getColumnValue(row, "jts_media_link") != null) {
            attributes.add(new Attribute("WA_MEDIA_LINK", (String) getColumnValue(row, "jts_media_link")));
        } else if (getColumnValue(row, "MEDIA_LINK") != null) {
            attributes.add(new Attribute("WA_MEDIA_LINK", (String) getColumnValue(row, "MEDIA_LINK")));
        }
        if (getColumnValue(row, "JTS_MEDIA_NAME") != null) {
            attributes.add(new Attribute("WA_MEDIA_NAME", (String) getColumnValue(row, "JTS_MEDIA_NAME")));
        }
        message.setAttributes(attributes);
        message.setTplId(tplId);
        message.setSubject((String) getColumnValue(row, "Subject_id"));
        message.setId((Long) getColumnValue(row, "ID"));
        message.setCreativeId((String) getColumnValue(row, "creative_id"));
        return message;
    }

    private WtsMessage populateMessageTemplate(WtsMessage message, Map<String, Object> row) {
        String headerText = message.getHeaderText();

        if (headerText.contains("{{1}}")) {
            String headerParam = (String) getColumnValue(row, "JTS_HEADER_PARAM1");
            if (headerParam == null || headerParam.isEmpty()) {
                handleError(FailedStatus.INVALID, MessageStatus.FAILED, message,  "Personalization value missing for header parameter JTS_HEADER_PARAM1");
                return null;
            }
            headerText = headerText.replace("{{1}}", headerParam);
            message.getAttributes().add(new Attribute("WA_HEADER_PARAM_VALUE_1", headerParam));
        }

        String bodyText = message.getBodyText();
        for (int i = 1; i <= 10; i++) {
            if (bodyText.contains("{{" + i + "}}")) {
                String bodyParam = (String) getColumnValue(row, "JTS_BODY_PARAM" + i);
                if (bodyParam == null || bodyParam.isEmpty()) {
                    handleError(FailedStatus.INVALID, MessageStatus.FAILED, message, "Personalization value missing for body parameter JTS_BODY_PARAM" + i);
                    return null;
                }
                bodyText = bodyText.replace("{{" + i + "}}", bodyParam);
                message.getAttributes().add(new Attribute("WA_BODY_PARAM_VALUE_" + i, bodyParam));
            }
        }

        message.setText(headerText + "|" + bodyText + "|" + message.getFooterText());
        return message;
    }

    private WtsMessage populateButtonParameters(WtsMessage message, Map<String, Object> row) {
        for (int i = 1; i <= 10; i++) {
            String buttonLink = (String) getColumnValue(row, "BUTTON_LINK_" + i);
            if (buttonLink != null && buttonLink.contains("{{") && buttonLink.contains("}}")) {
                String buttonParam = (String) getColumnValue(row, "JTS_BUTTON_" + i + "_PARAM_VALUE");
                if (buttonParam == null || buttonParam.isEmpty()) {
                    handleError(FailedStatus.INVALID, MessageStatus.FAILED, message, "Personalization value missing for button parameter JTS_BUTTON_" + i + "_PARAM_VALUE");
                    return null;
                }
                message.getAttributes().add(new Attribute("WA_BUTTON_PARAM_VALUE_" + i + "_1", buttonParam));
            }
        }
        return message;
    }

    private void handleError(FailedStatus failedStatus, MessageStatus messageStatus, WtsMessage message, String errorMessage) {
        log.error(errorMessage);
        log.debug("Creating record of invalid message with ID {} and status {}", message.getId(), failedStatus.toString());
        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getInsertToErrorTable())) {
                stmt.setString(1, message.getContactId() != null ? message.getExternalId() : "null");
                stmt.setString(2, errorMessage.substring(0, Math.min(errorMessage.length(), 36)));
                stmt.executeUpdate();
            }
        } catch (SQLException e) {
            log.error("Exception during executing query", e);
        }
        updateDataTable(messageStatus, message, errorMessage);
    }

    private void updateDataTable(MessageStatus status, WtsMessage message, String errorMessage) {
        log.debug("Updating record with ID {} to status {}", message.getId(), status.getStatus());
        try (Connection conn = dataSource.getConnection()) {
            //TODO doplnit vsechny sloupecky
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getUpdateDataTable())) {
                stmt.setString(1, status.getStatus());
                stmt.setString(2, message.getContactId() != null ? message.getExternalId() : null);
                if (message.getTplId() == null) {
                    stmt.setNull(3, Types.BIGINT);
                } else {
                    stmt.setLong(3, message.getTplId());
                }
                
                stmt.setString(4, message.getMessageCode());
                stmt.setString(5, errorMessage);
                stmt.setLong(6, message.getId());
                stmt.executeUpdate();
            }
        } catch (SQLException e) {
            log.error("Exception during executing query", e);
        }
    }

    private boolean isMessageExpired(WtsMessage message) {
        if (message.getExpires() == null) {
            return false;
        }
        log.debug("Checking if message is expired. expires = [{}]", message.getExpires());
        return message.getExpires().isBefore(ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone())));
    }

    private static boolean isMessageInvalid(WtsMessage message) {
        log.debug("Checking if message is invalid (messageCode or tplId is null), messageCode = [{}], tplId = [{}]",
                message.getMessageCode(), message.getTplId());
        return message.getMessageCode() == null || message.getTplId() == null;
    }
}

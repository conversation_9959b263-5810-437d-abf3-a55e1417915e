package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.model.message.WtsMessage;
import com.homecredit.model.messageattribues.WtsMessageAttributes;
import com.homecredit.service.ReceiverAgentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile("wts")
public class WtsReceiverAgentService extends ReceiverAgentService {

    private final ApplicationConfig applicationConfig;
    private final DataSource dataSource;
    private final ObjectMapper mapper;

    @Override
    @Async
    public void processEvent(String eventMessage) {
        log.info("Received event: {}", eventMessage);
        //configuration message from ci360
        if (eventMessage.startsWith("CFG")) {
            return;
        }

        try {
            WtsMessage message = mapper.readValue(eventMessage, WtsMessage.class);

            if (message.getAttributes() == null) {
                log.warn("Message has no 'attributes'. Cannot parse message.");
                return;
            }
            if (message.getAttributes().getEventName() == null) {
                log.warn("Message attributes have no 'eventName'. Cannot parse message.");
                return;
            }

            if (message.getAttributes().getEventName().toUpperCase().startsWith(applicationConfig.getCi360().getPrefix())) {
                log.info("Event received, will be processing. eventName: {}", message.getAttributes().getEventName());
                processMessage(message.getAttributes());
            } else {
                log.error("CH_01 - Event name {} is not matching with {}% prefix", message.getAttributes().getEventName(), applicationConfig.getCi360().getPrefix());
            }
        } catch (JsonProcessingException e) {
            log.error("CH_02 - Validation error - missing mandatory attributes in payload", e);
        }
    }

    private void processMessage(WtsMessageAttributes attributes) {
        log.debug("Creating new record with data {}", attributes);

        String leadId = getLeadId(attributes.getDatahubId(), attributes.getResponseTrackingCode(), attributes.getTimestamp());
        LocalDateTime expiresOnDttm = getExpiresOnDttm(attributes.getTaskpropWtsTimeValidDuration());

        Map<String, String> creativeContent = parseCreativeContent(attributes.getCreativeContent());

        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().getStoreData())) {
                setTimestampParameter(stmt, 1, Timestamp.valueOf(expiresOnDttm));
                setStringParameter(stmt, 2, attributes.getDatahubId());
                setStringParameter(stmt, 3, attributes.getSubjectId());
                setStringParameter(stmt, 4, attributes.getCustomerId());
                setStringParameter(stmt, 5, attributes.getVid());
                setStringParameter(stmt, 6, attributes.getRecipient());
                setStringParameter(stmt, 7, leadId);
                setStringParameter(stmt, 8, getOptionalFromCreativeContent(creativeContent, "JTS_BODY_PARAM1"));
                setStringParameter(stmt, 9, getOptionalFromCreativeContent(creativeContent, "JTS_BODY_PARAM2"));
                setStringParameter(stmt, 10, getOptionalFromCreativeContent(creativeContent, "JTS_BODY_PARAM3"));
                setStringParameter(stmt, 11, getOptionalFromCreativeContent(creativeContent, "JTS_BODY_PARAM4"));
                setStringParameter(stmt, 12, getOptionalFromCreativeContent(creativeContent, "JTS_BODY_PARAM5"));
                setStringParameter(stmt, 13, getOptionalFromCreativeContent(creativeContent, "JTS_BODY_PARAM6"));
                setStringParameter(stmt, 14, getOptionalFromCreativeContent(creativeContent, "JTS_BODY_PARAM7"));
                setStringParameter(stmt, 15, getOptionalFromCreativeContent(creativeContent, "JTS_BODY_PARAM8"));
                setStringParameter(stmt, 16, getOptionalFromCreativeContent(creativeContent, "JTS_BODY_PARAM9"));
                setStringParameter(stmt, 17, getOptionalFromCreativeContent(creativeContent, "JTS_BODY_PARAM10"));
                setStringParameter(stmt, 18, getOptionalFromCreativeContent(creativeContent, "JTS_HEADER_PARAM1"));
                setStringParameter(stmt, 19, getOptionalFromCreativeContent(creativeContent, "JTS_BUTTON_1_PARAM_VALUE"));
                setStringParameter(stmt, 20, getOptionalFromCreativeContent(creativeContent, "JTS_BUTTON_2_PARAM_VALUE"));
                setStringParameter(stmt, 21, getOptionalFromCreativeContent(creativeContent, "JTS_BUTTON_3_PARAM_VALUE"));
                setStringParameter(stmt, 22, getOptionalFromCreativeContent(creativeContent, "JTS_BUTTON_4_PARAM_VALUE"));
                setStringParameter(stmt, 23, getOptionalFromCreativeContent(creativeContent, "JTS_BUTTON_5_PARAM_VALUE"));
                setStringParameter(stmt, 24, getOptionalFromCreativeContent(creativeContent, "JTS_BUTTON_6_PARAM_VALUE"));
                setStringParameter(stmt, 25, getOptionalFromCreativeContent(creativeContent, "JTS_BUTTON_7_PARAM_VALUE"));
                setStringParameter(stmt, 26, getOptionalFromCreativeContent(creativeContent, "JTS_BUTTON_8_PARAM_VALUE"));
                setStringParameter(stmt, 27, getOptionalFromCreativeContent(creativeContent, "JTS_BUTTON_9_PARAM_VALUE"));
                setStringParameter(stmt, 28, getOptionalFromCreativeContent(creativeContent, "JTS_BUTTON_10_PARAM_VALUE"));
                setStringParameter(stmt, 29, getOptionalFromCreativeContent(creativeContent, "JTS_MEDIA_CONTENT_TYPE"));
                setStringParameter(stmt, 30, getOptionalFromCreativeContent(creativeContent, "JTS_MEDIA_LINK"));
                setStringParameter(stmt, 31, getOptionalFromCreativeContent(creativeContent, "JTS_MEDIA_NAME"));
                setStringParameter(stmt, 32, attributes.getResponseTrackingCode());
                setStringParameter(stmt, 33, attributes.getTaskId());
                setStringParameter(stmt, 34, attributes.getTaskVersionId());
                setStringParameter(stmt, 35, attributes.getCreativeId());
                setStringParameter(stmt, 36, attributes.getMessageId());
                stmt.executeUpdate();
            } catch (SQLException e) {
                log.error("CH_03_2 - DB - not possible to write payload to DB", e);
            }
        } catch (SQLException e) {
            log.error("CH_03_1 - DB - not possible to establish DB connection", e);
        }
    }

    private LocalDateTime getExpiresOnDttm(String taskpropSmsTimeValidDuration) {
        if (taskpropSmsTimeValidDuration == null) {
            return null;
        }
        int validDuration = Integer.parseInt(taskpropSmsTimeValidDuration); //time in minutes
        return LocalDateTime.now().plusMinutes(validDuration).plusDays(1).minusSeconds(1);
    }
}

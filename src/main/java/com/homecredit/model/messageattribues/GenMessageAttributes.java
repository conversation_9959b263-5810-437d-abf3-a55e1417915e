package com.homecredit.model.messageattribues;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

@Data
@ToString(callSuper = true)
public class GenMessageAttributes extends BaseMessageAttributes {

    @JsonProperty("contact_info")
    private String contactInfo;
    @JsonProperty("contract_code")
    private String contractCode;
    @JsonProperty("contributing_guid_1")
    private String contributingGuid1;
    @JsonProperty("custom_info2")
    private String customInfo2;
    @JsonProperty("custom_info3")
    private String customInfo3;
    @JsonProperty("custom_info4")
    private String customInfo4;
    private String gender;
    private String language1;
    @JsonProperty("last_first_name")
    private String lastFirstName;
    @JsonProperty("SITPhoneNum")
    private String SITPhoneNum;
    private String sentInbox;
    private String sentPush;
    private String sentPushWithPicture;
    private String sentSms;
    private String sentTSO;
    private String sentWhatsapp;
    @JsonProperty("TASKPROP_custom_channel_TSO")
    private String taskpropCustomChannelTso;
    @JsonProperty(value = "TASKPROP_custom_TSO_comm_end_days", required = true)
    private String taskpropCustomTsoCommEndDays;
    @JsonProperty(value = "TASKPROP_custom_TSO_comm_start_days", required = true)
    private String taskpropCustomTsoCommStartDays;
    @JsonProperty("TASKPROP_custom_TSO_callback_req")
    private String taskpropCustomTsoCallbackReq;
    @JsonProperty(value = "TASKPROP_custom_TSO_call_list_type_code", required = true)
    private String taskpropCustomTsoCallListTypeCode; //TODO tohle chybi v example payloadu
    @JsonProperty("TASKPROP_custom_TSO_call_list_name")
    private String taskpropCustomTsoCallListName;
    @JsonProperty("TASKPROP_custom_TSO_daily_from")
    private String taskpropCustomTsoDailyFrom; //TODO tohle chybi v example payloadu
    @JsonProperty("TASKPROP_custom_TSO_daily_till")
    private String taskpropCustomTsoDailyTill; //TODO tohle chybi v example payloadu
    @JsonProperty("TASKPROP_custom_TSO_daily_from_tpl")
    private String taskpropCustomTsoDailyFromTpl;
    @JsonProperty("TASKPROP_custom_TSO_daily_till_tpl")
    private String taskpropCustomTsoDailyTillTpl;
    @JsonProperty("TASKPROP_custom_TSO_priority")
    private String taskpropCustomTsoPriority;
    @JsonProperty("TASKPROP_custom_TSO_script_type")
    private String taskpropCustomTsoScriptType;
    @JsonProperty("TASKPROP_custom_TSO_template_id")
    private String taskpropCustomTsoTemplateId;
    @JsonProperty("TASKPROP_custom_TSO_tpl_id_usage")
    private String taskpropCustomTsoTplIdUsage;
    private String text;

    @JsonIgnore
    private Map<String, String> outboundProperties = new HashMap<>();

    @JsonAnySetter
    public void add(String property, String value){
        outboundProperties.put(property, value);
    }
}

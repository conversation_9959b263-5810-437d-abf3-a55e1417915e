package com.homecredit.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
public class DMPushMessage extends Message {

    private String phoneNumber;
    private String workflow;
    @JsonProperty("messageAttribute")
    private String messageAttributeString;
    private String messageId;
    @JsonProperty("additionalData")
    private String additionalDataString;

    @JsonIgnore
    private DMPushMessageAttribute messageAttribute;
    @JsonIgnore
    private DMPushAdditionalData additionalData;

    @JsonIgnore
    private ZonedDateTime generatedDateTime;
    @JsonIgnore
    private Long id;
    @JsonIgnore
    private String contactId;
    @JsonIgnore
    private String identityId;
    @JsonIgnore
    private String visitorId;
    @JsonIgnore
    private String leadId;
    @JsonIgnore
    private ZonedDateTime validFrom;
    @JsonIgnore
    private ZonedDateTime validTo;
}

package com.homecredit.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

@Data
public class WtsMessage extends Message {

    private String externalId;
    private String recipient;
    private String systemCode;
    private String messageCode;
    private String cuid;
    private String text;
    private ZonedDateTime expires;
    private List<Attribute> attributes;
    private String priority;
    private String reportLevel;

    @JsonIgnore
    private ZonedDateTime generatedDateTime;
    @JsonIgnore
    private Long tplId;
    @JsonIgnore
    private String subject;
    @JsonIgnore
    private Long id;
    @JsonIgnore
    private String creativeId;
    @JsonIgnore
    private String contactId;

    @JsonIgnore
    private String headerText;
    @JsonIgnore
    private String bodyText;
    @JsonIgnore
    private String footerText;
    @JsonIgnore
    private String tplLanguage;
    @JsonIgnore
    private String jatisTemplateId;
    @JsonIgnore
    private String jatisAccountId;
    @JsonIgnore
    private String channel;
    @JsonIgnore
    private String feature;
    @JsonIgnore
    private String buttonType1;
    @JsonIgnore
    private String buttonType2;
    @JsonIgnore
    private String buttonType3;
    @JsonIgnore
    private String buttonType4;
    @JsonIgnore
    private String buttonType5;
    @JsonIgnore
    private String buttonType6;
    @JsonIgnore
    private String buttonType7;
    @JsonIgnore
    private String buttonType8;
    @JsonIgnore
    private String buttonType9;
    @JsonIgnore
    private String buttonType10;
    @JsonIgnore
    private String jtsMediaContentType;
    @JsonIgnore
    private String jtsMediaLink;
}

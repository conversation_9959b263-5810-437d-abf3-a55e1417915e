hci:
  agents:
    GEN:
      enabled: false
    IBM:
      enabled: true
    SMS:
      enabled: true
    PSH:
      enabled: true
    LMA:
      enabled: false
    IDLMA:
      enabled: false
    WTS:
      enabled: true
    DM_PSH:
      enabled: true
    DM_SMS:
      enabled: true
    DM_GEN:
      enabled: true
  cron:
    load-cache: "0 */5 * * * *" # 5 minutes
    process-data: "*/5 * * * * *"  # 5 seconds
  query:
    GEN:
      load-cache: SELECT * FROM INT_COMMUNICATION.DICT_CALL_LIST_TYPE where status  = 'A'
      load-cache-code-call-list-name: SELECT * FROM INT_COMMUNICATION.DICT_CALL_LIST_NAME
      get-non-processed-records: with base_ch as (
        select contact_id,
        Identity_id,
        lead_Id,
        ROW_NUMBER() over (partition by lead_Id order by contact_id) rnleadch
        from CDM2.CIE_CONTACT_HISTORY_STREAM
        where contact_dt >= sysdate - 1
        ),
        base_queue as (
        select id,
        status,
        GenerateDateDttm,
        Identity_id,
        Subject_id,
        Customer_id,
        Visitor_id,
        name_call_list,
        communication_start,
        communication_end,
        campaign_start,
        campaign_end,
        daily_from,
        daily_till,
        priority,
        code_call_list_type,
        script_type,
        is_callback_request,
        Contact_id,
        LeadID,
        Rtc_id,
        Task_id,
        Task_version_id,
        Creative_id,
        Tpl_id,
        ProcessedDttm,
        Error_Message,
        ROW_NUMBER() over (partition by leadId order by id) rnleadch
        from INT_COMMUNICATION.GEN_INT_Q_SYNC
        where (status = 'N' or status is null) and GENERATEDATEDTTM >= sysdate - 1
        )
        select
        q.id,
        q.status,
        q.GenerateDateDttm,
        q.Identity_id,
        q.Subject_id,
        q.Customer_id,
        q.Visitor_id,
        q.name_call_list,
        q.communication_start,
        q.communication_end,
        q.campaign_start,
        q.campaign_end,
        q.daily_from,
        q.daily_till,
        q.priority,
        q.code_call_list_type,
        q.script_type,
        q.is_callback_request,
        q.Contact_id,
        q.LeadID,
        q.Rtc_id,
        q.Task_id,
        q.Task_version_id,
        q.Creative_id,
        q.Tpl_id,
        q.ProcessedDttm,
        q.Error_Message,
        nvl(q.customer_id,q.subject_id) as cuid,
        ch.contact_id
        from base_queue q
        left join base_ch ch on ch.lead_Id = q.leadId and ch.rnleadch = q.rnleadch
      get-business-attributes: Select Attribute_name, Attribute_Value from INT_COMMUNICATION.GEN_INT_Q_SYNC_PARAM where leadID = ?
      insert-to-error-table: INSERT INTO INT_COMMUNICATION.CIE_INT_RESPONSE_BY_CONTACT_ID (Response_Dttm, source_channel_cd, Contact_id, source_response_cd, EXTERNAL_INFO_1_ID, Source_System_Cd) VALUES (sysdate, 'GEN', ?, 'INVALID', ?, 'AGENT_GEN')
      update-data-table: UPDATE INT_COMMUNICATION.GEN_INT_Q_SYNC SET STATUS = ?, ERROR_MESSAGE = ?, CONTACT_ID = ?, code_call_list_type = ?, ProcessedDttm = sysdate WHERE ID = ?
    IBM:
      get-non-processed-records: with base_ch as (
        select contact_id,
        Identity_id,
        lead_Id,
        ROW_NUMBER() over (partition by lead_Id order by contact_id) rnleadch
        from CDM2.CIE_CONTACT_HISTORY_STREAM
        where contact_dt >= sysdate - 1
        ),
        base_queue as (
        select
        id,
        status,
        generatedatedttm,
        timerangemindttm,
        timerangemaxdttm,
        identity_id,
        subject_id,
        customer_id,
        visitor_id,
        text,
        title,
        imageurl,
        should_notify,
        ibm_categoryid,
        contact_id,
        leadid,
        rtc_id,
        task_id,
        task_version_id,
        creative_id,
        processeddttm,
        error_message,
        ROW_NUMBER() over (partition by leadId order by id) rnleadch
        from INT_COMMUNICATION.IBM_INT_Q_SYNC
        where (status = 'N' or status is null) and GENERATEDATEDTTM >= sysdate - 1
        )
        select
        q.id,
        q.status,
        q.generatedatedttm,
        q.timerangemindttm,
        q.timerangemaxdttm,
        q.identity_id,
        q.subject_id,
        q.customer_id,
        q.visitor_id,
        nvl(q.customer_id,q.subject_id) as cuid,
        q.text,
        q.title,
        q.imageurl,
        q.should_notify,
        q.ibm_categoryid,
        ch.contact_id,
        token.GMA_user_id as GMA_user_id,
        q.leadid,
        q.rtc_id,
        q.task_id,
        q.task_version_id,
        q.creative_id,
        q.processeddttm,
        q.error_message
        from base_queue q
        left join base_ch ch on ch.lead_Id = q.leadId and ch.rnleadch = q.rnleadch
        left join cdm2.GMA_SAS_INFO token on token.datahub_id = q.Identity_id
      insert-to-error-table: INSERT INTO INT_COMMUNICATION.CIE_INT_RESPONSE_BY_CONTACT_ID (Response_Dttm, source_channel_cd, Contact_id, source_response_cd, EXTERNAL_INFO_1_ID, Source_System_Cd) VALUES (sysdate, 'IBM', ?, ?, ?, 'AGENT_IBM')
      update-data-table: UPDATE INT_COMMUNICATION.IBM_INT_Q_SYNC SET STATUS = ?, CONTACT_ID = ?, ERROR_MESSAGE = ?, ProcessedDttm = sysdate WHERE ID = ?
    PSH:
      get-non-processed-records: with base_ch as (
        select contact_id,
        Identity_id,
        lead_Id,
        ROW_NUMBER() over (partition by lead_Id order by contact_id) rnleadch
        from CDM2.CIE_CONTACT_HISTORY_STREAM
        where contact_dt >= sysdate - 1
        ),
        base_queue as (
        select id,
        status,
        generatedatedttm,
        timerangemindttm,
        timerangemaxdttm,
        identity_id,
        subject_id,
        customer_id,
        visitor_id,
        text,
        title,
        do_not_store_in_inbox,
        ibm_categoryId,
        leadid,
        rtc_id,
        task_id,
        task_version_id,
        creative_id,
        tpl_id,
        messagecode,
        processeddttm,
        error_message,
        ROW_NUMBER() over (partition by leadId order by id) rnleadch
        from INT_COMMUNICATION.PSH_int_q_sync
        where (status = 'N' or status is null) and GENERATEDATEDTTM >= sysdate - 1
        )
        select
        q.id,
        q.status,
        q.generatedatedttm,
        q.timerangemindttm,
        q.timerangemaxdttm,
        q.identity_id,
        q.subject_id,
        q.customer_id,
        q.visitor_id,
        nvl(q.customer_id,q.subject_id) as cuid,
        q.text,
        q.title,
        q.do_not_store_in_inbox,
        q.ibm_categoryId,
        ch.contact_id,
        q.leadid,
        q.rtc_id,
        q.task_id,
        q.task_version_id,
        q.creative_id,
        c2t.Tpl_Id  as TPL_ID,
        dictPUSH.message_Code as messageCode,
        dictPUSH.LINK as LINK,
        dictPUSH.PRIORITY_LIST as PRIORITY_LIST,
        dictPUSH.IMAGE as IMAGE,
        dictPUSH.SCREEN_LABEL as SCREEN_LABEL,
        dictPUSH.GMA_INBOX as GMA_INBOX,
        dictPUSH.GMA_INBOX_CATEGORY as GMA_INBOX_CATEGORY,
        token.GMA_push_token as GMA_push_token,
        token.GMA_user_id as GMA_user_id,
        q.processeddttm,
        q.error_message
        from base_queue q
        left join base_ch ch on ch.lead_Id = q.leadId and ch.rnleadch = q.rnleadch
        left join CDM2.CIE_CREATIVE2TEMPLATE c2t on c2t.creative_id = q.creative_id
        left join INT_COMMUNICATION.DICT_PUSH_TEMPLATE dictPUSH on c2t.Tpl_id = dictPUSH.Tpl_id
        left join CDM2.GMA_SAS_INFO token on token.datahub_id = q.Identity_id
      insert-to-error-table: INSERT INTO INT_COMMUNICATION.CIE_INT_RESPONSE_BY_CONTACT_ID (Response_Dttm, source_channel_cd, Contact_id, source_response_cd, EXTERNAL_INFO_1_ID, Source_System_Cd) VALUES (sysdate, 'PSH', ?, ?, ?, 'AGENT_GEN')
      update-data-table: UPDATE INT_COMMUNICATION.PSH_INT_Q_SYNC SET STATUS = ?, CONTACT_ID = ?, TPL_ID = ?, MESSAGECODE = ?, ERROR_MESSAGE = ?, ProcessedDttm = sysdate WHERE ID = ?
      insert-to-creative-table: INSERT INTO CDM2.CIE_CREATIVE2TEMPLATE (CREATIVE_ID, CREATIVE_CD, TPL_ID, CHANNEL_CD) VALUES (?, ?, ?, ?)
    SMS:
      get-non-processed-records: with base_ch as (
        select contact_id,
          Identity_id,
          lead_Id,
          ROW_NUMBER() over (partition by lead_Id order by contact_id) rnleadch
          from CDM2.CIE_CONTACT_HISTORY_STREAM
          where contact_dt >= sysdate - 1
        ),
        base_queue as (
          select id,
          status,
          generatedatedttm,
          timerangemindttm,
          timerangemaxdttm,
          expiresondttm,
          identity_id,
          subject_id,
          customer_id,
          visitor_id,
          priority,
          msg_text,
          phone_number,
          contact_id,
          leadid,
          rtc_id,
          task_id,
          task_version_id,
          creative_id,
          tpl_id,
          messagecode,
          processeddttm,
          error_message,
          ROW_NUMBER() over (partition by leadId order by id) rnleadch
          from INT_COMMUNICATION.SMS_INT_Q_SYNC
          where (status = 'N' or status is null) and GENERATEDATEDTTM >= sysdate - 1
        )
        select
          q.id,
          q.status,
          q.generatedatedttm,
          q.timerangemindttm,
          q.timerangemaxdttm,
          q.expiresondttm,
          q.identity_id,
          q.subject_id,
          q.customer_id,
          q.visitor_id,
          nvl(q.customer_id,q.subject_id) as cuid,
          q.priority,
          q.msg_text,
          q.phone_number,
          ch.contact_id,
          q.leadid,
          q.rtc_id,
          q.task_id,
          q.task_version_id,
          q.creative_id,
          c2t.Tpl_Id  as TPL_ID,
          dictSMS.message_Code as MESSAGE_CODE,
          q.processeddttm,
          q.error_message
        from base_queue q
        left join base_ch ch on ch.lead_Id = q.leadId and ch.rnleadch = q.rnleadch
        left join CDM2.CIE_CREATIVE2TEMPLATE c2t on c2t.creative_id = q.creative_id
        left join INT_COMMUNICATION.DICT_SMS_TEMPLATE dictSMS on c2t.Tpl_id = dictSMS.Tpl_id
      insert-to-error-table: INSERT INTO INT_COMMUNICATION.CIE_INT_RESPONSE_BY_CONTACT_ID (Response_Dttm, source_channel_cd, Contact_id, source_response_cd, Source_System_Cd) VALUES (sysdate, 'SMS', ?, ?, 'AGENT_SMS')
      update-data-table: UPDATE INT_COMMUNICATION.SMS_INT_Q_SYNC SET STATUS = ?, CONTACT_ID = ?, TPL_ID = ?, MESSAGECODE = ?, ERROR_MESSAGE = ?, ProcessedDttm = sysdate WHERE ID = ?
      insert-to-creative-table: INSERT INTO CDM2.CIE_CREATIVE2TEMPLATE (CREATIVE_ID, CREATIVE_CD, TPL_ID, CHANNEL_CD) VALUES (?, ?, ?, ?)
    LMA:
      get-non-processed-records: with base_ch as (
        select contact_id,
        Identity_id,
        lead_Id,
        ROW_NUMBER() over (partition by lead_Id order by contact_id) rnleadch
        from CDM2.CIE_CONTACT_HISTORY_STREAM
        where contact_dt >= sysdate - 1
        ),
        base_queue as (
        select id,
        status,
        generatedatedttm,
        timerangemindttm,
        timerangemaxdttm,
        identity_id,
        subject_id,
        customer_id,
        visitor_id,
        text,
        title,
        imageurl,
        deeplink,
        buttontext,
        leadid,
        rtc_id,
        task_id,
        task_version_id,
        creative_id,
        messagecode,
        processeddttm,
        error_message,
        ROW_NUMBER() over (partition by leadId order by id) rnleadch
        from INT_COMMUNICATION.PSH_LMA_INT_Q_SYNC
        where (status = 'N' or status is null) and GENERATEDATEDTTM >= sysdate - 1
        )
        select
        q.id,
        q.status,
        q.generatedatedttm,
        q.timerangemindttm,
        q.timerangemaxdttm,
        q.identity_id,
        q.subject_id,
        q.customer_id,
        q.visitor_id,
        nvl(q.customer_id,q.subject_id) as cuid,
        q.text,
        q.title,
        q.imageurl,
        q.deeplink,
        q.buttontext,
        ch.contact_id,
        q.leadid,
        q.rtc_id,
        q.task_id,
        q.task_version_id,
        q.creative_id,
        q.messagecode,
        q.processeddttm,
        q.error_message
        from base_queue q
        left join base_ch ch on ch.lead_Id = q.leadId and ch.rnleadch = q.rnleadch
      insert-to-error-table: INSERT INTO INT_COMMUNICATION.CIE_INT_RESPONSE_BY_CONTACT_ID (Response_Dttm, source_channel_cd, Contact_id, source_response_cd, EXTERNAL_INFO_1_ID, Source_System_Cd) VALUES (sysdate, 'PSH', ?, ?, ?, 'AGENT_GEN')
      update-data-table: UPDATE INT_COMMUNICATION.PSH_LMA_INT_Q_SYNC SET STATUS = ?, CONTACT_ID = ?, MESSAGECODE = ?, ERROR_MESSAGE = ?, ProcessedDttm = sysdate WHERE ID = ?
    IDLMA:
      get-non-processed-records: with base_ch as (
        select contact_id,
        Identity_id,
        lead_Id,
        ROW_NUMBER() over (partition by lead_Id order by contact_id) rnleadch
        from CDM2.CIE_CONTACT_HISTORY_STREAM
        where contact_dt >= sysdate - 1
        ),
        base_queue as (
        select id,
        status,
        generatedatedttm,
        timerangemindttm,
        timerangemaxdttm,
        identity_id,
        subject_id,
        customer_id,
        visitor_id,
        text,
        title,
        imageurl,
        deeplink,
        buttontext,
        leadid,
        rtc_id,
        task_id,
        task_version_id,
        creative_id,
        messagecode,
        processeddttm,
        error_message,
        ROW_NUMBER() over (partition by leadId order by id) rnleadch
        from INT_COMMUNICATION.PSH_LMA_INT_Q_SYNC
        where (status = 'N' or status is null) and GENERATEDATEDTTM >= sysdate - 1
        )
        select
        q.id,
        q.status,
        q.generatedatedttm,
        q.identity_id,
        q.subject_id,
        q.customer_id,
        q.visitor_id,
        nvl(q.customer_id,q.subject_id) as cuid,
        q.PhoneNumber,
        q.Contract_Num,
        q.Title,
        q.ShortMessage,
        q.FullDetailMessage,
        q.ImageURL,
        q.Deeplink,
        q.Category,
        q.SubCategory,
        q.Remark,
        ch.contact_id,
        q.leadid,
        q.rtc_id,
        q.task_id,
        q.task_version_id,
        q.creative_id,
        q.processeddttm,
        q.error_message
        from base_queue q
        left join base_ch ch on ch.lead_Id = q.leadId and ch.rnleadch = q.rnleadch
      insert-to-error-table: INSERT INTO INT_COMMUNICATION.CIE_INT_RESPONSE_BY_CONTACT_ID (Response_Dttm, source_channel_cd, Contact_id, source_response_cd, Source_System_Cd) VALUES (sysdate, 'PSH', ?, ?, 'AGENT_PSH_LMA')
      update-data-table: UPDATE INT_COMMUNICATION.PSH_LMA_INT_Q_SYNC SET STATUS = ?, CONTACT_ID = ?, MESSAGEID = ?, ERROR_MESSAGE = ?, ProcessedDttm = sysdate WHERE ID = ?
    WTS:
      get-non-processed-records: with base_ch as (
        select contact_id,
        Identity_id,
        lead_Id,
        ROW_NUMBER() over (partition by lead_Id order by contact_id) rnleadch
        from CDM2.CIE_CONTACT_HISTORY_STREAM
        where contact_dt >= sysdate - 1
        ),
        base_queue as (
        select id,
        status,
        generatedatedttm,
        expiresondttm,
        identity_id,
        subject_id,
        customer_id,
        visitor_id,
        phone_number,
        contact_id,
        leadid,
        jts_body_param1,
        jts_body_param2,
        jts_body_param3,
        jts_body_param4,
        jts_body_param5,
        jts_body_param6,
        jts_body_param7,
        jts_body_param8,
        jts_body_param9,
        jts_body_param10,
        jts_header_param1,
        jts_button_1_param_value,
        jts_button_2_param_value,
        jts_button_3_param_value,
        jts_button_4_param_value,
        jts_button_5_param_value,
        jts_button_6_param_value,
        jts_button_7_param_value,
        jts_button_8_param_value,
        jts_button_9_param_value,
        jts_button_10_param_value,
        jts_media_content_type,
        jts_media_link,
        jts_media_name,
        msg_text,
        rtc_id,
        task_id,
        task_version_id,
        creative_id,
        message_id,
        tpl_id,
        messagecode,
        processeddttm,
        error_message,
        ROW_NUMBER() over (partition by leadId order by id) rnleadch
        from INT_COMMUNICATION.SMS_INT_Q_SYNC
        where (status = 'N' or status is null) and GENERATEDATEDTTM >= sysdate - 1
        )
        select
        q.id,
        q.status,
        q.generatedatedttm,
        q.expiresondttm,
        q.identity_id,
        q.subject_id,
        q.customer_id,
        q.visitor_id,
        q.phone_number,
        q.contact_id,
        q.leadid,
        q.jts_body_param1,
        q.jts_body_param2,
        q.jts_body_param3,
        q.jts_body_param4,
        q.jts_body_param5,
        q.jts_body_param6,
        q.jts_body_param7,
        q.jts_body_param8,
        q.jts_body_param9,
        q.jts_body_param10,
        q.jts_header_param1,
        q.jts_button_1_param_value,
        q.jts_button_2_param_value,
        q.jts_button_3_param_value,
        q.jts_button_4_param_value,
        q.jts_button_5_param_value,
        q.jts_button_6_param_value,
        q.jts_button_7_param_value,
        q.jts_button_8_param_value,
        q.jts_button_9_param_value,
        q.jts_button_10_param_value,
        q.jts_media_content_type,
        q.jts_media_link,
        q.jts_media_name,
        q.msg_text,
        q.rtc_id,
        q.task_id,
        q.task_version_id,
        q.creative_id,
        q.message_id,
        q.messagecode,
        q.processeddttm,
        q.error_message,
        c2t.Tpl_Id,
        dictWTS.TPL_LANGUAGE,
        dictWTS.JATIS_TEMPLATE_ID,
        dictWTS.JATIS_ACCOUNT_ID,
        dictWTS.MESSAGE_CODE,
        dictWTS.CHANNEL,
        dictWTS.PRIORITY_LIST,
        dictWTS.FEATURE,
        dictWTS.BUTTON_TYPE_1,
        dictWTS.BUTTON_TYPE_2,
        dictWTS.BUTTON_TYPE_3,
        dictWTS.BUTTON_TYPE_4,
        dictWTS.BUTTON_TYPE_5,
        dictWTS.BUTTON_TYPE_6,
        dictWTS.BUTTON_TYPE_7,
        dictWTS.BUTTON_TYPE_8,
        dictWTS.BUTTON_TYPE_9,
        dictWTS.BUTTON_TYPE_10,
        dictWTS.BUTTON_LINK_1,
        dictWTS.BUTTON_LINK_2,
        dictWTS.BUTTON_LINK_3,
        dictWTS.BUTTON_LINK_4,
        dictWTS.BUTTON_LINK_5,
        dictWTS.BUTTON_LINK_6,
        dictWTS.BUTTON_LINK_7,
        dictWTS.BUTTON_LINK_8,
        dictWTS.BUTTON_LINK_9,
        dictWTS.BUTTON_LINK_10,
        dictWTS.MEDIA_CONTENT_TYPE,
        dictWTS.MEDIA_LINK,
        dictWTS.HEADER_TEXT,
        dictWTS.BODY_TEXT,
        dictWTS.FOOTER_TEXT,
        from base_queue q
        left join base_ch ch on ch.lead_Id = q.leadId and ch.rnleadch = q.rnleadch
        left join CDM2.CIE_CREATIVE2TEMPLATE c2t on c2t.creative_id = q.creative_id
        left join INT_COMMUNICATION.DICT_WTS_TEMPLATE dictWTS on c2t.Tpl_id = dictWTS.Tpl_id
      insert-to-error-table: INSERT INTO INT_COMMUNICATION.CIE_INT_RESPONSE_BY_CONTACT_ID (Response_Dttm, source_channel_cd, Contact_id, source_response_cd, Source_System_Cd) VALUES (sysdate, 'WTS', ?, ?, 'AGENT_WTS')
      update-data-table: UPDATE INT_COMMUNICATION.SMS_INT_Q_SYNC SET 
        STATUS = ?, 
        CONTACT_ID = ?, 
        TPL_ID = ?, 
        TPL_LANGUAGE = ?,
        JATIS_TPL_ID = ?,
        JATIS_ACCOUNT_ID = ?,
        MESSAGECODE = ?,
        CHANNEL = ?,
        PRIORITY = ?,
        FEATURE = ?,
        BUTTON_TYPE_1 = ?,
        BUTTON_TYPE_2 = ?,
        BUTTON_TYPE_3 = ?,
        BUTTON_TYPE_4 = ?,
        BUTTON_TYPE_5 = ?,
        BUTTON_TYPE_6 = ?,
        BUTTON_TYPE_7 = ?,
        BUTTON_TYPE_8 = ?,
        BUTTON_TYPE_9 = ?,
        BUTTON_TYPE_10 = ?,
        JTS_MEDIA_CONTENT_TYPE = ?,
        JTS_MEDIA_LINK = ?,
        MSG_TEXT = ?,
        ERROR_MESSAGE = ?, 
        ProcessedDttm = sysdate 
        WHERE ID = ?
      insert-to-creative-table: INSERT INTO CDM2.CIE_CREATIVE2TEMPLATE (CREATIVE_ID, CREATIVE_CD, TPL_ID, CHANNEL_CD) VALUES (?, ?, ?, ?)
    DM_PSH:
      get-non-processed-records: with base_ch as (
          select 
          contact_id, 
          Identity_id,
          lead_Id, 
          visitor_id, 
          subject_id, 
          rtc_id, 
          ROW_NUMBER() over (partition by subject_id, rtc_id order by contact_id ) rnleadch
          from cdm2.cie_contact_history_stream
          where contact_dt >= sysdate-1
        ),
        base_queue as (
          select 
          id,
          phonenumber,
          timerangemindttm,
          timerangemaxdttm,
          GENERATEDATEDTTM,
          contract_num,
          title,
          fulldetailmessage,
          shortmessage,
          imageurl,
          deeplink,
          messageid,
          category,
          subcategory,
          remark,
          customer_id,
          subject_id, 
          rtc_id,
          ROW_NUMBER() over (partition by subject_id, rtc_id order by id) rnleadch
          from INT_COMMUNICATION.PSH_LMA_DM_INT_Q_SYNC
          where status = 'N' or status is null
          and GENERATEDATEDTTM >= sysdate-1
        )
        select
        q.id,
        q.phonenumber,
        q.timerangemindttm,
        q.timerangemaxdttm,
        q.GENERATEDATEDTTM,
        q.contract_num,
        q.title,
        q.fulldetailmessage,
        q.shortmessage,
        q.imageurl,
        q.deeplink,
        q.messageid,
        q.category,
        q.subcategory,
        q.remark,
        nvl(q.customer_id,q.subject_id) as cuid,
        q.subject_id,
        q.rtc_id,
        ch.lead_id,
        ch.contact_id,
        ch.identity_id,
        ch.visitor_id
        from base_ch ch
        join base_queue q on ch.subject_id = q.subject_id and ch.rtc_id = q.rtc_id and ch.rnleadch = q.rnleadch
      insert-to-error-table: INSERT INTO INT_COMMUNICATION.CIE_INT_RESPONSE_BY_CONTACT_ID (Response_Dttm, source_channel_cd, Contact_id, source_response_cd, EXTERNAL_INFO_1_ID, Source_System_Cd) VALUES (sysdate, 'PSH', ?, ?, ?, 'PSHDM_MERO')
      update-data-table: UPDATE INT_COMMUNICATION.PSH_LMA_DM_INT_Q_SYNC SET STATUS = ?, CONTACT_ID = ?, identity_id = ?, visitor_id = ?, leadid = ?, MESSAGEID = ?, ERROR_MESSAGE = ?, ProcessedDttm = sysdate WHERE ID = ?
    DM_SMS:
      get-non-processed-records: with base_ch as (
        select
        contact_id,
        Identity_id,
        lead_Id,
        visitor_id,
        subject_id,
        rtc_id,
        ROW_NUMBER() over (partition by subject_id, rtc_id order by contact_id ) rnleadch
        from cdm2.cie_contact_history_stream
        where contact_dt >= sysdate-1
        ),
        base_queue as (
        select
        id,
        timerangemindttm,
        timerangemaxdttm,
        GENERATEDATEDTTM,
        ExpiresOnDttm,
        messageCode,
        phone_number,
        msg_text,
        customer_id,
        subject_id,
        rtc_id,
        leadId,
        ROW_NUMBER() over (partition by subject_id, rtc_id order by id) rnleadch
        from INT_COMMUNICATION.SMS_DM_INT_Q_SYNC
        where status = 'N' or status is null
        and GENERATEDATEDTTM >= sysdate-1
        )
        select
        q.id,
        q.timerangemindttm,
        q.timerangemaxdttm,
        q.GENERATEDATEDTTM,
        q.ExpiresOnDttm,
        q.messageCode,
        q.phone_number,
        q.msg_text,
        nvl(q.customer_id,q.subject_id) as cuid,
        q.subject_id,
        q.rtc_id,
        ch.lead_id,
        ch.contact_id,
        ch.identity_id,
        ch.visitor_id
        from base_ch ch
        join base_queue q on ch.lead_Id = q.leadId and ch.rnleadch = q.rnleadch
      insert-to-error-table: INSERT INTO INT_COMMUNICATION.CIE_INT_RESPONSE_BY_CONTACT_ID (Response_Dttm, source_channel_cd, Contact_id, source_response_cd, EXTERNAL_INFO_1_ID, Source_System_Cd) VALUES (sysdate, 'SMS', ?, ?, ?, 'AGENT_SMS')
      update-data-table: UPDATE INT_COMMUNICATION.SMS_DM_INT_Q_SYNC SET STATUS = ?, CONTACT_ID = ?, identity_id = ?, visitor_id = ?, lead_id = ?, ERROR_MESSAGE = ?, ProcessedDttm = sysdate WHERE ID = ?
    DM_GEN:
      load-cache: SELECT * FROM INT_COMMUNICATION.DICT_CALL_LIST_TYPE where status  = 'A'
      load-cache-code-call-list-name: SELECT * FROM INT_COMMUNICATION.DICT_CALL_LIST_NAME
      get-non-processed-records: with base_ch as (
        select
        contact_id,
        Identity_id,
        lead_Id,
        visitor_id,
        subject_id,
        rtc_id,
        ROW_NUMBER() over (partition by subject_id, rtc_id order by contact_id ) rnleadch
        from cdm2.cie_contact_history_stream
        where contact_dt >= sysdate-1
        ),
        base_queue as (
        select
        id,
        GENERATEDATEDTTM,
        customer_id,
        subject_id,
        rtc_id,
        leadId,
        is_callback_request,
        communication_start,
        communication_end,
        campaign_start,
        campaign_end,
        code_call_list_type,
        daily_from,
        daily_till,
        priority,
        name_call_list,
        Tpl_id,
        ROW_NUMBER() over (partition by subject_id, rtc_id order by id) rnleadch
        from INT_COMMUNICATION.GEN_DM_INT_Q_SYNC
        where status = 'N' or status is null
        and GENERATEDATEDTTM >= sysdate-1
        )
        select
        q.id,
        q.GENERATEDATEDTTM,
        nvl(q.customer_id,q.subject_id) as cuid,
        q.subject_id,
        q.rtc_id,
        q.is_callback_request,
        q.communication_start,
        q.communication_end,
        q.campaign_start,
        q.campaign_end,
        q.code_call_list_type,
        q.daily_from,
        q.daily_till,
        q.priority,
        q.name_call_list,
        q.Tpl_id,
        ch.lead_id,
        ch.contact_id,
        ch.identity_id,
        ch.visitor_id
        from base_ch ch
        join base_queue q on ch.subject_id = q.subject_id and ch.rtc_id = q.rtc_id and ch.rnleadch = q.rnleadch
      get-business-attributes: Select Attribute_name, Attribute_Value from INT_COMMUNICATION.GEN_DM_INT_Q_SYNC_PARAM where subject_id = ? and rtc_id = ?
      insert-to-error-table: INSERT INTO INT_COMMUNICATION.CIE_INT_RESPONSE_BY_CONTACT_ID (Response_Dttm, source_channel_cd, Contact_id, source_response_cd, EXTERNAL_INFO_1_ID, Source_System_Cd) VALUES (sysdate, 'TSO', ?, 'INVALID', ?, 'AGENT_GEN')
      update-data-table: UPDATE INT_COMMUNICATION.GEN_DM_INT_Q_SYNC SET STATUS = ?, CONTACT_ID = ?, identity_id = ?, visitor_id = ?, leadId = ?, ERROR_MESSAGE = ?, ProcessedDttm = sysdate WHERE ID = ?

  payload:
    GEN:
      switch-id: 101
      call-source: 360
    IBM:
      system-code: 360
    PSH:
      system-code: 360
      report-level: DELIVERY_REPORTS
    SMS:
      system-code: 360
      report-level: CHANGE_REPORTS
      sas-message-type: sms_type
    LMA:
      system-code: 360
      report-level: DELIVERY_REPORTS
      logical-application: HCCreditCard
      priority: HIGH
    IDLMA:
      system-code: MERO
      workflow: MERO3_PUSH
      partnerID: MSS
    WTS:
      system-code: 360
      report-level: DELIVERY_REPORTS
    DM_PSH:
      system-code: MERO
      workflow: MERO3_PUSH
      partnerID: MSS
    DM_SMS:
      system-code: 360
      report-level: DELIVERY_REPORTS
      sas-message-type: sms_type
    DM_GEN:
      switch-id: 101
      call-source: 360

  timezone: Asia/Jakarta
  cuid-timeout: 120
  contact-id-timeout: 5

  rabbit:
    IBM:
      routing-key: ext-message-v1
      exchange: koyal.inbox.ext-messages
    PSH:
      routing-key: send-push-message.v2
      exchange: mss.incoming
    SMS:
      routing-key: send-sms-message.v2
      exchange: mss.incoming
    LMA:
      routing-key: send-push-message.v2
      exchange: mss.incoming
    IDLMA:
      routing-key: mero.send.dynamic.message.queue.request
      exchange: mero-send-dynamic-message-exchange
    WTS:
      routing-key: send-wts-message.v2
      exchange: mss.incoming
    DM_PSH:
      routing-key: mero.send.dynamic.message.queue.request
      exchange: mero-send-dynamic-message-exchange
    DM_SMS:
      routing-key: send-sms-message.v2
      exchange: mss.incoming

  extapigwservice:
    url: https://extapigwservice-mum-prod.ci360.sas.com/marketingDesign/creatives/
    token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJRCI6IjI4YzRkYWI0ZGIwMDAxMThiNTFjZDRmMiJ9.dZyyk1jBtujn7ZIoLx9qaZmEVZahmx-7IBCr9hrx9l4
    read-timeout: 1000
    connect-timeout: 1000

spring:
  datasource:
    url: **************************************************************
    username: MA_TEMP
    password: MA_TEMP123
    driver-class-name: oracle.jdbc.OracleDriver
  # kafka config used only for GEN and DM_GEN agent
  kafka:
    bootstrap-servers: cpkafka01-id00c1.id.infra:9092,cpkafka02-id00c1.id.infra:9092,cpkafka03-id00c1.id.infra:9092
    template:
      default-topic: gen.affinity.sas_360_ci_data.v1 # only genesys uses kafka, set as default
    properties:
      security:
        protocol: SASL_SSL
      sasl:
        mechanism: SCRAM-SHA-256
        jaas:
          config: org.apache.kafka.common.security.scram.ScramLoginModule required username="SAS_User" password="SAS_User";
  rabbitmq:
    host: harabbit-hosel.id.infra
    port: 5672
    username: sas_user
    password: sas_user
    virtual-host: id00c1
  # if channel uses different rabbitMQ host than rest of them, specify it here and enable multirabbit
  multirabbitmq:
    enabled: true
    connections:
      IDLMA:
        host: rabbitmq03-local.id.infra
        port: 5672
        username: id.sas-user
        password: fATRnJHBHdjA
      DM_PSH:
        host: rabbitmq03-local.id.infra
        port: 5672
        username: id.sas-user
        password: fATRnJHBHdjA

management:
  endpoints:
    web:
      exposure:
        include: [ "prometheus" ]
server:
  port: 11001

logging:
  level:
    com.homecredit: DEBUG

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cz.airbank.sas-agent-kafka</groupId>
        <artifactId>sas-agent-kafka-parent</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>avro-schema</artifactId>

    <properties>
        <version.avro.obs>cometdev.174</version.avro.obs>
        <version.avro.ams>cometdev.103</version.avro.ams>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.apicurio</groupId>
            <artifactId>apicurio-registry-serdes-avro-serde</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>unpack</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>cz.airbank.wsdl</groupId>
                                    <artifactId>obs_avro</artifactId>
                                    <version>${version.avro.obs}</version>
                                    <outputDirectory>${project.build.directory}</outputDirectory>
                                    <includes>**\/*.avsc</includes>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>cz.airbank.wsdl</groupId>
                                    <artifactId>ams_avro</artifactId>
                                    <version>${version.avro.ams}</version>
                                    <outputDirectory>${project.build.directory}</outputDirectory>
                                    <includes>**\/*.avsc</includes>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro-maven-plugin</artifactId>
                <version>1.11.3</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>schema</goal>
                        </goals>
                        <configuration>
                            <sourceDirectory>${project.build.directory}/avro</sourceDirectory>
                            <outputDirectory>${project.build.directory}/generated-sources</outputDirectory>
                            <stringType>String</stringType>
                            <createOptionalGetters>true</createOptionalGetters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>